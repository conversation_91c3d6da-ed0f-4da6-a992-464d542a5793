<div class="card card-custom gutter-b" id="manage-user">
  <app-card-header
    [cardTitle]="cardTitle"
    [cardSubTitle]="cardSubTitle"
    [buttons]="buttons"
    [showSplitButton]="true"
    [splitButtonDropDownOption]="splitButtonDropDownOption"
  ></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="users?.user_authorizations"
      [lazy]="true"
      (onLazyLoad)="getUsers($event)"
      dataKey="id"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      (onSort)="sortColumn()"
      [filterDelay]="0"
      [sortField]="sortFieldName"
      [sortOrder]="sortOrderNumber"
    >
      <!-- <p-table responsiveLayout="scroll"  #dt [value]="users?.user_authorizations"  dataKey="id" [loading]="loading" styleClass="p-datatable-customers" [filterDelay]="0"> -->
      <ng-template pTemplate="header">
        <tr>
          <th id="name" class="header-width" pSortableColumn="first_name">
            Name
            <p-sortIcon field="first_name"></p-sortIcon>
          </th>
          <th id="email" class="header-width" pSortableColumn="user_authorization?.email">
            Email
            <p-sortIcon field="email"></p-sortIcon>
          </th>
          <th id="role" class="header-width" pSortableColumn="Email">
            Role
            <p-sortIcon field="Email"></p-sortIcon>
          </th>
          <th id="status" class="header-width" pSortableColumn="Email">
            Status
            <p-sortIcon field="Email"></p-sortIcon>
          </th>
          <th id="actions" class="header-width">Actions</th>
        </tr>
        <tr *ngIf="showFilter">
          <!-- <form #f="ngForm"> -->
          <!-- <span ngModelGroup="dataFilter" #nameCtrl="ngModelGroup"> -->
          <th>
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.name_search" (click)="clearNameFilter()"></em>
              <input pInputText type="text" [(ngModel)]="dataFilter.name_search" (input)="filter()" class="p-column-filter" />
            </span>
          </th>
          <th>
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.email" (click)="clearEmailFilter()"></em>
              <input pInputText type="text" [(ngModel)]="dataFilter.email" (input)="filter()" class="p-column-filter" />
            </span>
          </th>
          <th>
            <span class="p-input-icon-right">
              <em class="pi pi-times" *ngIf="dataFilter.role" (click)="clearRoleFilter()"></em>
              <input pInputText type="text" [(ngModel)]="dataFilter.role" (input)="filter()" class="p-column-filter" />
            </span>
          </th>
          <th id="searchStatus">
            <p-dropdown
              class="search-status-wrapper"
              appendTo="body"
              [options]="statuses"
              [(ngModel)]="dataFilter.is_active"
              (onChange)="filter()"
              styleClass="p-column-filter pi-icon"
              placeholder="Status"
            >
            </p-dropdown>
          </th>
          <th></th>
          <!-- </span> -->
          <!-- </form> -->
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-user>
        <tr>
          <td>
            {{ user?.user_authorization?.first_name + ' ' + user?.user_authorization?.last_name }}
          </td>
          <td>
            {{ user?.user_authorization?.email }}
          </td>
          <td>
            {{ (user?.user_authorization?.self_service_storage?.role | titlecase) ?? 'None' }}
          </td>
          <td [ngClass]="user?.user_authorization?.active ? 'green-text' : 'red-text'">
            {{ user?.user_authorization?.active ? 'Active' : 'Inactive' }}
          </td>
          <td>
            <!-- TODO: Update permissions -->
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_USER; disableEvent: true"
              [routerLink]="[appRoutes.UPDATE_USER, user?.user_authorization?.id]"
            >
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_USER; disableEvent: true"
              (click)="confirmDeleteType(user?.user_authorization?.id)"
            >
              <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="5" class="center-align">No Users found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog header="Delete Expense Type" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog-expense" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this user?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteUser()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [(visible)]="permissonDeniedModel"
  header="Permission Denied"
  [style]="{ width: '400px' }"
  class="confirm-dialog-expense"
  [modal]="true"
  [draggable]="false"
  [resizable]="false"
>
  <h5 class="p-m-0">Requires Special Admin Access- contact your system administrator</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-save" (click)="closePermissionModel()" [isSubmitting]="isSubmitting">Ok</button>
    </div>
  </ng-template>
</p-dialog>
