<div class="card card-custom gutter-b" id="manageRole">
  <app-card-header [cardTitle]="cardTitle" [cardSubTitle]="cardSubTitle" [buttons]="buttons"></app-card-header>
  <div class="card-body">
    <p-table
      responsiveLayout="scroll"
      #dt
      [value]="roles"
      [lazy]="true"
      (onLazyLoad)="getRoles()"
      dataKey="id"
      [rows]="10"
      [loading]="loading"
      styleClass="p-datatable-customers"
      [filterDelay]="0"
      [sortField]="'name'"
      [sortOrder]="-1"
    >
      <ng-template pTemplate="header">
        <tr>
          <th id="role" class="header-width">Roles</th>
          <th id="description" class="header-width">Description</th>
          <th id="actions" class="header-width text-center" colspan="2">Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-role>
        <tr>
          <td>
            {{ role?.name }}
          </td>
          <td>
            {{ role?.description }}
          </td>
          <td class="text-right pr-0">
            <a
              class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
              *hasAnyPermission="permissionModules.MANAGE_ROLE; disableEvent: true"
              [routerLink]="[appRoutes.EDIT_ROLE, role?.id]"
              [state]="{ blockUpdateDelete: role.blockUpdateDelete }"
            >
              <span title="Edit" [inlineSVG]="'assets/media/svg/icons/edit.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
            </a>
          </td>
          <td>
            <ng-container *ngIf="!role?.blockUpdateDelete; else disabledDelete">
              <a
                class="btn btn-icon btn-light btn-icon-light btn-sm btn-hover-primary"
                *hasAnyPermission="permissionModules.MANAGE_ROLE; disableEvent: true"
                (click)="confirmDeleteRole(role?.id)"
              >
                <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
              </a>
            </ng-container>
            <ng-template #disabledDelete>
              <span title="Archive" [inlineSVG]="'assets/media/svg/icons/archive.svg'" cacheSVG="true" class="disabled-icon btn-icon-light svg-icon svg-icon-md"></span>
            </ng-template>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="4" class="center-align">No Roles found.</td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog header="Delete Project" [(visible)]="showDeleteDialog" [modal]="true" class="confirm-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <h5 class="p-m-0">Are you sure you want to delete this role?</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-cancel" (click)="closeModal()">No</button>
      <button type="button" class="btn-save" (click)="deleteRole()" [isSubmitting]="isSubmitting">Yes</button>
    </div>
  </ng-template>
</p-dialog>

<p-dialog
  [visible]="permissonDeniedModel"
  header="Permission Denied"
  [style]="{ width: '400px' }"
  class="confirm-dialog-expense"
  [modal]="true"
  [baseZIndex]="10000"
  [draggable]="false"
  [resizable]="false"
>
  <h5 class="p-m-0">Requires Special Admin Access- contact your system administrator</h5>
  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center">
      <button type="button" class="btn-save" (click)="closePermissionModel()" [isSubmitting]="isSubmitting">Ok</button>
    </div>
  </ng-template>
</p-dialog>
